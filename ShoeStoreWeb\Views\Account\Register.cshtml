@model ShoeStoreWeb.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header text-center bg-success text-white">
                    <h4><i class="fas fa-user-plus me-2"></i>Đăng ký tài khoản</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input asp-for="Name" class="form-control" placeholder="Nhập họ tên" />
                                </div>
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input asp-for="Email" class="form-control" placeholder="Nhập email" />
                                </div>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                </div>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Xác nhận mật khẩu" />
                                </div>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Phone" class="form-label"></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại" />
                                </div>
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">Vai trò</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                    <select class="form-select" name="Role">
                                        <option value="customer" selected>Khách hàng</option>
                                        <option value="admin">Quản trị viên</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                <textarea asp-for="Address" class="form-control" rows="2" placeholder="Nhập địa chỉ (tùy chọn)"></textarea>
                            </div>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus me-2"></i>Đăng ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" class="text-decoration-none">Đăng nhập ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
