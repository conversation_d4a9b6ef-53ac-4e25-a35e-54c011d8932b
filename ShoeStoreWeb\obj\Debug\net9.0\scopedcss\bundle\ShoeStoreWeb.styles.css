/* _content/ShoeStoreWeb/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-dr9madd251] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-dr9madd251] {
  color: #0077cc;
}

.btn-primary[b-dr9madd251] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-dr9madd251], .nav-pills .show > .nav-link[b-dr9madd251] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-dr9madd251] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-dr9madd251] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-dr9madd251] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-dr9madd251] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-dr9madd251] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
