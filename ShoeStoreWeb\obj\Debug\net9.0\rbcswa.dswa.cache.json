{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["aOLj5gDSkca5PXVTwuDSfscB4o3TwvKRLw7echVoFtE=", "/e5vOA+Umz08IVUPSXDN5pS3uvSpqw1dLd5vpxWAoog=", "BoQ/l9eteF9LGjFjpahKSqoIyUxguuDFZD307yUVXCk=", "V/JuBm4eQRvyRcvOpr6G9SJ4TodNokAW1B/344hgXY8=", "K9mE+Hu6AeZYe2MwnevAWFAHR6VC7AI/jMxK+lOXHXM=", "KU7NFdvOsXYzDeF3c8y8yjYaG/Nq+mHid9F3rsQu8y8=", "nVqYNzzScg4uctYHvZ/ePs3C1T1q9qq85e7agUOs+ds=", "YpbNTYzPVWj6X5b4/hnJ7UoZG4z9JXdYTw14eObZ7fM=", "KKzSIBOyboSRJL9aVfhIm44FMuJTu4QXSQiF/0PybZ4=", "vI4nua3xN+UxAC5nnoZ8P3ChfX2b95qCygutAGuWaKM=", "ib1GSdUVQ13KidtD9lqd1WXJtYrTRg1j1mXYpq7FMq0=", "8OIB9Q2hHSE/MTQK0QLCGebtQhLcXmNWV6k+y43txfU=", "ygrJIKXbMOCojF5x69aF4mM6Vz5CsY8H+TxLIgeNXL4=", "nVKA+jZXLJN+fKmiAQ814fhjMY3ABkKAklUBEYYpbZ8=", "V7RE4pEog5mcYlcGG65dx4pp9EjyqDkGfy0YQNXzzWg=", "TpEQfgfyqk95oSIyHR5wYMqtx5/6y30qpTBGay+uKIM=", "GzCvYkPvUG3JBn0Gm7ShCMXejKsdFcTn3XvOWPMYB9o=", "v9CuHnixgNeBQqARXqg0HQamkPizMcr4yMGpedfTncA=", "DmJsTKUu3kOZvbh9RuFpN26ZqhkBat0v/FpZ8v1KV9Y=", "IVUeXv0Pd8v1TfpSSJy8S9cKiJXHpF/x2CWSvAnSDmw=", "p4FJb0LrLCIFLMNFx0HStm6dZBqCh8BksKz8lOVCRvE=", "jsDdgpzc8emUc8wRi8TDXnJqmdg2gbVgoTcSWaXKwsA=", "Wa1L9MRXJ7HWnEesCoO2rDChx761bcuI+Vw0rlm97xA=", "sgGEKRqKcT9ahQbnSThzY2Ikjr2EzF4BidQencPPvW4=", "FKrOAk0ZTGDXteGsqCXXnFgsM1DnDu+03BAoYFfcYV0=", "lxI7CB817Qrj3jkfgLdqsEMNMIE1aWIsip350uev5c4=", "iDHekXNmW17zsQAyYAFFo9MwNzkcVEfEfYNpzuCqSY8=", "uv9AXxf/r462bysisylRkMOd1MYoFVubINewE+uZzfA=", "D4pzLjJfTtttmeCtXzOAVJh5yt8VoEidsLaC0HQa3ss=", "CffQnTg4V9HUw41GxVH8zWxGpXvZo2rJzmRBKiPMPLU=", "Za7AadrSoaJt6r/aCKlwMXvyls0e34FrDgf5Ga+kz/o=", "KWFBwR6rAs0lgCv5oQuNmebZeznnfA2lmHq034VdKag=", "onYI9Q6Ai6ZfFUH3FelgzpN0T0QygKy6+XYGKyDCrZI=", "+TTPkjibNx6KwcKVglHOpgXYWvNDTTIwMW7DePw8ntM=", "4z7mRrkS3BwvcEvYj00RX65AZOm8nNJ+XCMbuAQvE/E=", "2sfDU0v4FmaxApqmEA2ZWsEdtbJmO+Hm/X3oVxb9Q2o=", "FLheXM/sA39N2M+mO8iRNy/rflaf3oMKuNK51dGvwcU=", "a5RlV2Sr49KcsPjBsyjHNQKpgBS96ccH5IOeU10BsaI=", "6yMz1u2Qcbwt5QX/fgBBdqCJbRWTqPWftscr+gTlJGk=", "mInVHoKEAYHYVu5Fi3XPm1xM7gUzWIIXVkiSawYg0O8=", "Ctr7yhST9xoru739aZqrAdkS/bR6rKbs4vDvdplXqOQ=", "vnPNSSYAZta+iQCumDcc1bXztel2+yNEeDIVTAsWCrw=", "hqQu35Qs8y1lHK93lEQgs8NxXCCDUNUO2T8V4xF2AHQ=", "1dS3sPCUxphS0uN9doRcRv7oaO281tGErQhXRaiIg0E=", "ThW5c158aSa65k4dWtnYx8G3FiPNT5i8Vfk0L8IAGxc=", "+1y7YY9/E402k+4Gu1PiVbN6QdjLn8l79p1Q7V8eCWQ=", "eC3JryA34txD7xZZI1asUW/lvE6YBJ70zmBn2soBy9Y=", "yeXfekvsyqEdikHVkiyBleAYB0Vzk1u5XbpQ8oQcuF8=", "I32NnF2ICmf0JfUPVYpjx8YWlXCMzxlEgDpqOqhFTPs=", "Xr1UC2pHqs+/jXc7Kw2b0BzyzuaifFF3n2qMxBOgXec=", "5aOkp2Er++o+z+L4QorcfTIFhFvLuPk+qn8bCwmwh1U=", "diWizyjqEjai45sk6lJqQdXCtNcBAThUbIPrpDqfz5M=", "UgxZCl0iDOY9UtsrdmicwwCEb0OF6te/G2Snxdm54Yo=", "m/pqOI9QnHsNELto4KXdS4p49s44W8QVhncosmM8Ei4=", "9oQwlTESnPcSQBPydIV1O1tAp0P2/q6v+iWF6pWRPPE=", "Amm+sa3OHXvknPyGWS0pmzsIAHrgr35QgyGSxvWjB8Y=", "iRahpPE07YYqG5Rw+GSjxSoibgCKq0Y64SwL77HCWrg=", "asLAWNE9bLRKn2rGL7u7DXcNEpvjLoiCiitDRrSD1Kg=", "Y6NffFBCWcPp1QnWIcGfKeLgK2eCZNI5HTdSsvww95Y=", "fNcJ1qMR+yTR3jPiEyfWrbB1ewVl5nLW2WcjtAyIMyA=", "G7EsqVR0jP8eUBZkIu+8TWi3RmaVJfq5aYnhLOY8Q5M=", "jS6CbNCpBY1hCKpYH9r7nM+KHI9k5vpWn5N7MTMFS7Y=", "BTdjy2pl7krHjfftjdZ3xwAMqIs7laozdQD7cpRPMPk=", "8aQ2SiIRNpRvSuMzOceuo6x1INL+2C5M1pGkSWwWPqQ="], "CachedAssets": {"aOLj5gDSkca5PXVTwuDSfscB4o3TwvKRLw7echVoFtE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\pxoorsi9bc-b9sayid5wm.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-31T17:39:05.3356569+00:00"}, "/e5vOA+Umz08IVUPSXDN5pS3uvSpqw1dLd5vpxWAoog=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\rr3npp9m9i-61n19gt1b8.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-31T17:39:05.3387099+00:00"}, "BoQ/l9eteF9LGjFjpahKSqoIyUxguuDFZD307yUVXCk=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\qllelq7g3g-xtxxf3hu2r.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-31T17:39:05.3397117+00:00"}, "V/JuBm4eQRvyRcvOpr6G9SJ4TodNokAW1B/344hgXY8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\3cvioh45zt-bqjiyaj88i.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-31T17:39:05.3417095+00:00"}, "K9mE+Hu6AeZYe2MwnevAWFAHR6VC7AI/jMxK+lOXHXM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\upiz5v9mzn-c2jlpeoesf.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-31T17:39:05.3691239+00:00"}, "KU7NFdvOsXYzDeF3c8y8yjYaG/Nq+mHid9F3rsQu8y8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o6ez2lxh7n-erw9l3u2r3.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-31T17:39:05.3356569+00:00"}, "nVqYNzzScg4uctYHvZ/ePs3C1T1q9qq85e7agUOs+ds=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o09gk1pahk-aexeepp0ev.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-31T17:39:05.3397117+00:00"}, "YpbNTYzPVWj6X5b4/hnJ7UoZG4z9JXdYTw14eObZ7fM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ua7ghhey30-d7shbmvgxk.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-31T17:39:05.3417095+00:00"}, "KKzSIBOyboSRJL9aVfhIm44FMuJTu4QXSQiF/0PybZ4=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\6xt0blbjn5-ausgxo2sd3.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-31T17:39:05.3497094+00:00"}, "vI4nua3xN+UxAC5nnoZ8P3ChfX2b95qCygutAGuWaKM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\yu66br00dp-k8d9w2qqmf.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-31T17:39:05.4021312+00:00"}, "ib1GSdUVQ13KidtD9lqd1WXJtYrTRg1j1mXYpq7FMq0=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\x1xe5e2xba-cosvhxvwiu.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-31T17:39:05.3377105+00:00"}, "8OIB9Q2hHSE/MTQK0QLCGebtQhLcXmNWV6k+y43txfU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vk1x6ai5jd-ub07r2b239.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-31T17:39:05.4031312+00:00"}, "ygrJIKXbMOCojF5x69aF4mM6Vz5CsY8H+TxLIgeNXL4=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\wybads5va9-fvhpjtyr6v.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-31T17:39:05.4076287+00:00"}, "nVKA+jZXLJN+fKmiAQ814fhjMY3ABkKAklUBEYYpbZ8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\tjqsm6rynn-b7pk76d08c.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-31T17:39:05.408628+00:00"}, "V7RE4pEog5mcYlcGG65dx4pp9EjyqDkGfy0YQNXzzWg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\bdz8hu14xm-fsbi9cje9m.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-31T17:39:05.411625+00:00"}, "TpEQfgfyqk95oSIyHR5wYMqtx5/6y30qpTBGay+uKIM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\tcgnjyt21g-rzd6atqjts.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-31T17:39:05.3356569+00:00"}, "GzCvYkPvUG3JBn0Gm7ShCMXejKsdFcTn3XvOWPMYB9o=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o1l3wdcnvi-ee0r1s7dh0.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-31T17:39:05.3467105+00:00"}, "v9CuHnixgNeBQqARXqg0HQamkPizMcr4yMGpedfTncA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\7fy4vfqxsr-dxx9fxp4il.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-31T17:39:05.3487114+00:00"}, "DmJsTKUu3kOZvbh9RuFpN26ZqhkBat0v/FpZ8v1KV9Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\xqchvnqto7-jd9uben2k1.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-31T17:39:05.3547112+00:00"}, "IVUeXv0Pd8v1TfpSSJy8S9cKiJXHpF/x2CWSvAnSDmw=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\hsavc1es29-khv3u5hwcm.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-31T17:39:05.3567104+00:00"}, "p4FJb0LrLCIFLMNFx0HStm6dZBqCh8BksKz8lOVCRvE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\rh47xz805w-r4e9w2rdcm.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-31T17:39:05.3407116+00:00"}, "jsDdgpzc8emUc8wRi8TDXnJqmdg2gbVgoTcSWaXKwsA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\7n0c5v8fmh-lcd1t2u6c8.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-31T17:39:05.3577096+00:00"}, "Wa1L9MRXJ7HWnEesCoO2rDChx761bcuI+Vw0rlm97xA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\0scf2iqsfb-c2oey78nd0.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-31T17:39:05.3647115+00:00"}, "sgGEKRqKcT9ahQbnSThzY2Ikjr2EzF4BidQencPPvW4=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\bsn72dv1a5-tdbxkamptv.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-31T17:39:05.3691239+00:00"}, "FKrOAk0ZTGDXteGsqCXXnFgsM1DnDu+03BAoYFfcYV0=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\mboahvzo8z-j5mq2jizvt.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-31T17:39:05.3891307+00:00"}, "lxI7CB817Qrj3jkfgLdqsEMNMIE1aWIsip350uev5c4=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\3n4yusy74o-06098lyss8.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-31T17:39:05.3367033+00:00"}, "iDHekXNmW17zsQAyYAFFo9MwNzkcVEfEfYNpzuCqSY8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\dv6dnr89a4-nvvlpmu67g.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-31T17:39:05.3417095+00:00"}, "uv9AXxf/r462bysisylRkMOd1MYoFVubINewE+uZzfA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vvr13nx95y-s35ty4nyc5.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-31T17:39:05.3497094+00:00"}, "D4pzLjJfTtttmeCtXzOAVJh5yt8VoEidsLaC0HQa3ss=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\uz1an8scju-pj5nd1wqec.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-31T17:39:05.3691239+00:00"}, "CffQnTg4V9HUw41GxVH8zWxGpXvZo2rJzmRBKiPMPLU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\v9vmf0v9ld-46ein0sx1k.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-31T17:39:05.4126258+00:00"}, "Za7AadrSoaJt6r/aCKlwMXvyls0e34FrDgf5Ga+kz/o=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vkvu63pjvq-v0zj4ognzu.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-31T17:39:05.3487114+00:00"}, "KWFBwR6rAs0lgCv5oQuNmebZeznnfA2lmHq034VdKag=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\axrss9f7w7-37tfw0ft22.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-31T17:39:05.3587119+00:00"}, "onYI9Q6Ai6ZfFUH3FelgzpN0T0QygKy6+XYGKyDCrZI=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\3h2atbma6q-hrwsygsryq.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-31T17:39:05.3841323+00:00"}, "+TTPkjibNx6KwcKVglHOpgXYWvNDTTIwMW7DePw8ntM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\mkwy1rxccx-pk9g2wxc8p.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-31T17:39:05.3801323+00:00"}, "4z7mRrkS3BwvcEvYj00RX65AZOm8nNJ+XCMbuAQvE/E=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\23degudgn3-ft3s53vfgj.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-31T17:39:05.394133+00:00"}, "2sfDU0v4FmaxApqmEA2ZWsEdtbJmO+Hm/X3oVxb9Q2o=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vuzpmf4nqj-6cfz1n2cew.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-31T17:39:05.394133+00:00"}, "FLheXM/sA39N2M+mO8iRNy/rflaf3oMKuNK51dGvwcU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o1qxeawl18-6pdc2jztkx.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-31T17:39:05.420422+00:00"}, "a5RlV2Sr49KcsPjBsyjHNQKpgBS96ccH5IOeU10BsaI=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\bv224iefpx-493y06b0oq.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-31T17:39:05.4234322+00:00"}, "6yMz1u2Qcbwt5QX/fgBBdqCJbRWTqPWftscr+gTlJGk=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\4m3xjkpgob-iovd86k7lj.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-31T17:39:05.4354333+00:00"}, "mInVHoKEAYHYVu5Fi3XPm1xM7gUzWIIXVkiSawYg0O8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\6mjhwyucru-vr1egmr9el.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-31T17:39:05.4414429+00:00"}, "Ctr7yhST9xoru739aZqrAdkS/bR6rKbs4vDvdplXqOQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\usxdhg9fdm-kbrnm935zg.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-31T17:39:05.3447114+00:00"}, "vnPNSSYAZta+iQCumDcc1bXztel2+yNEeDIVTAsWCrw=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ib0iz4nzab-jj8uyg4cgr.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-31T17:39:05.3517135+00:00"}, "hqQu35Qs8y1lHK93lEQgs8NxXCCDUNUO2T8V4xF2AHQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ombufyxdly-y7v9cxd14o.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-31T17:39:05.3691239+00:00"}, "1dS3sPCUxphS0uN9doRcRv7oaO281tGErQhXRaiIg0E=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o4bq1pnmpn-notf2xhcfb.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-31T17:39:05.3811316+00:00"}, "ThW5c158aSa65k4dWtnYx8G3FiPNT5i8Vfk0L8IAGxc=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\5h4hs2vcat-h1s4sie4z3.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-31T17:39:05.3901324+00:00"}, "+1y7YY9/E402k+4Gu1PiVbN6QdjLn8l79p1Q7V8eCWQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\yn17788w4f-63fj8s7r0e.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-31T17:39:05.3377105+00:00"}, "eC3JryA34txD7xZZI1asUW/lvE6YBJ70zmBn2soBy9Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\gyrxrr75zm-0j3bgjxly4.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-31T17:39:05.3437101+00:00"}, "yeXfekvsyqEdikHVkiyBleAYB0Vzk1u5XbpQ8oQcuF8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\pgjlnj6v2y-47otxtyo56.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-31T17:39:05.3477106+00:00"}, "I32NnF2ICmf0JfUPVYpjx8YWlXCMzxlEgDpqOqhFTPs=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\jdfd9k497x-4v8eqarkd7.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-31T17:39:05.3527131+00:00"}, "Xr1UC2pHqs+/jXc7Kw2b0BzyzuaifFF3n2qMxBOgXec=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\5d4tutp538-356vix0kms.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-31T17:39:05.3547112+00:00"}, "5aOkp2Er++o+z+L4QorcfTIFhFvLuPk+qn8bCwmwh1U=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\2il2nct2xi-83jwlth58m.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-31T17:39:05.3377105+00:00"}, "diWizyjqEjai45sk6lJqQdXCtNcBAThUbIPrpDqfz5M=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vtcyatt9zz-mrlpezrjn3.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-31T17:39:05.3457116+00:00"}, "UgxZCl0iDOY9UtsrdmicwwCEb0OF6te/G2Snxdm54Yo=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ofed9h70kf-lzl9nlhx6b.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-31T17:39:05.3477106+00:00"}, "m/pqOI9QnHsNELto4KXdS4p49s44W8QVhncosmM8Ei4=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\4tta4wkn09-ag7o75518u.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-31T17:39:05.3557104+00:00"}, "9oQwlTESnPcSQBPydIV1O1tAp0P2/q6v+iWF6pWRPPE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ej6efyh3fr-x0q3zqp4vz.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-31T17:39:05.3567104+00:00"}, "Amm+sa3OHXvknPyGWS0pmzsIAHrgr35QgyGSxvWjB8Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\o87200jw74-0i3buxo5is.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-31T17:39:05.4169237+00:00"}, "iRahpPE07YYqG5Rw+GSjxSoibgCKq0Y64SwL77HCWrg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\qtzxlhekax-o1o13a6vjx.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-31T17:39:05.3647115+00:00"}, "asLAWNE9bLRKn2rGL7u7DXcNEpvjLoiCiitDRrSD1Kg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\ndoowxj985-ttgo8qnofa.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-31T17:39:05.3711331+00:00"}, "Y6NffFBCWcPp1QnWIcGfKeLgK2eCZNI5HTdSsvww95Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\i6ggaeyqyd-2z0ns9nrw6.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-31T17:39:05.3851305+00:00"}, "fNcJ1qMR+yTR3jPiEyfWrbB1ewVl5nLW2WcjtAyIMyA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\vmeqhyfc58-muycvpuwrr.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-31T17:39:05.3891307+00:00"}, "G7EsqVR0jP8eUBZkIu+8TWi3RmaVJfq5aYnhLOY8Q5M=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\kvuva2c90q-87fc7y1x7t.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-31T17:39:05.4056261+00:00"}, "jS6CbNCpBY1hCKpYH9r7nM+KHI9k5vpWn5N7MTMFS7Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\71w0yf3vna-mlv21k5csn.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-31T17:39:05.3891307+00:00"}, "BTdjy2pl7krHjfftjdZ3xwAMqIs7laozdQD7cpRPMPk=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\9k06tp3ipx-akypiiye06.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "ShoeStoreWeb#[.{fingerprint=akypiiye06}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ShoeStoreWeb.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "myg3jhpsoz", "Integrity": "nt95F0nNBfZdKO6AeGxO7kU+dc/byCGlfEiY9szCngg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ShoeStoreWeb.styles.css", "FileLength": 541, "LastWriteTime": "2025-07-31T17:39:05.3901324+00:00"}, "8aQ2SiIRNpRvSuMzOceuo6x1INL+2C5M1pGkSWwWPqQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\qj4b2d3vrx-akypiiye06.gz", "SourceId": "ShoeStoreWeb", "SourceType": "Computed", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "ShoeStoreWeb#[.{fingerprint=akypiiye06}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ShoeStoreWeb.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "myg3jhpsoz", "Integrity": "nt95F0nNBfZdKO6AeGxO7kU+dc/byCGlfEiY9szCngg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ShoeStoreWeb.bundle.scp.css", "FileLength": 541, "LastWriteTime": "2025-07-31T17:39:05.3901324+00:00"}}, "CachedCopyCandidates": {}}