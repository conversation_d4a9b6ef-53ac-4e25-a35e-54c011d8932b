﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace ShoeStoreWeb.Migrations
{
    /// <inheritdoc />
    public partial class ConvertToAppleStore : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Products_Purpose",
                table: "Products");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "product-1");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "product-2");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "product-3");

            migrationBuilder.DropColumn(
                name: "Purpose",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON>",
                table: "OrderItems");

            migrationBuilder.RenameColumn(
                name: "Sizes",
                table: "Products",
                newName: "StorageOptions");

            migrationBuilder.AddColumn<string>(
                name: "DisplaySize",
                table: "Products",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "KeyFeatures",
                table: "Products",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Processor",
                table: "Products",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ProductType",
                table: "Products",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "StorageOption",
                table: "OrderItems",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "Brand", "Category", "Colors", "CreatedAt", "Description", "DisplaySize", "Featured", "Images", "InStock", "KeyFeatures", "Name", "OriginalPrice", "Price", "Processor", "ProductType", "Rating", "Reviews", "StorageOptions" },
                values: new object[,]
                {
                    { "ipad-pro-12-9", "Apple", "iPad", "[\"Space Gray\", \"Silver\"]", new DateTime(2024, 1, 3, 0, 0, 0, 0, DateTimeKind.Utc), "iPad Pro 12.9-inch với chip M2, màn hình Liquid Retina XDR", "12.9 inch", true, "[\"https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/ipad-pro-12-select-wifi-spacegray-202210.jpg\"]", true, "[\"Liquid Retina XDR\", \"Apple Pencil 2\", \"Magic Keyboard\", \"5G\"]", "iPad Pro 12.9-inch", null, 31990000m, "Apple M2", "iPad Pro", 4.5999999999999996, 142, "[\"128GB\", \"256GB\", \"512GB\", \"1TB\", \"2TB\"]" },
                    { "iphone-15-pro", "Apple", "iPhone", "[\"Natural Titanium\", \"Blue Titanium\", \"White Titanium\", \"Black Titanium\"]", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "iPhone 15 Pro với chip A17 Pro, camera 48MP và khung titan cao cấp", "6.1 inch", true, "[\"https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-15-pro-finish-select-202309-6-1inch-naturaltitanium.jpg\"]", true, "[\"Camera 48MP\", \"Action Button\", \"USB-C\", \"Titanium Design\"]", "iPhone 15 Pro", 30990000m, 28990000m, "A17 Pro", "iPhone 15 Pro", 4.7999999999999998, 256, "[\"128GB\", \"256GB\", \"512GB\", \"1TB\"]" },
                    { "macbook-air-m3", "Apple", "Mac", "[\"Midnight\", \"Starlight\", \"Space Gray\", \"Silver\"]", new DateTime(2024, 1, 2, 0, 0, 0, 0, DateTimeKind.Utc), "MacBook Air 13-inch với chip M3, thiết kế siêu mỏng và pin 18 giờ", "13.6 inch", true, "[\"https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/macbook-air-midnight-select-20220606.jpg\"]", true, "[\"Chip M3\", \"Pin 18 giờ\", \"Liquid Retina Display\", \"MagSafe\"]", "MacBook Air 13-inch M3", null, 32990000m, "Apple M3", "MacBook Air", 4.7000000000000002, 189, "[\"256GB\", \"512GB\", \"1TB\", \"2TB\"]" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Products_ProductType",
                table: "Products",
                column: "ProductType");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Products_ProductType",
                table: "Products");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "ipad-pro-12-9");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "iphone-15-pro");

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: "macbook-air-m3");

            migrationBuilder.DropColumn(
                name: "DisplaySize",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "KeyFeatures",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "Processor",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "ProductType",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "StorageOption",
                table: "OrderItems");

            migrationBuilder.RenameColumn(
                name: "StorageOptions",
                table: "Products",
                newName: "Sizes");

            migrationBuilder.AddColumn<string>(
                name: "Purpose",
                table: "Products",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Size",
                table: "OrderItems",
                type: "TEXT",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "Brand", "Category", "Colors", "CreatedAt", "Description", "Featured", "Images", "InStock", "Name", "OriginalPrice", "Price", "Purpose", "Rating", "Reviews", "Sizes" },
                values: new object[,]
                {
                    { "product-1", "Nike", "men", "[\"Đen\", \"Trắng\", \"Xám\"]", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "Giày thể thao Nike Air Max 270 với công nghệ đệm khí tiên tiến", true, "[\"https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff3-46b4-949b-2d16af2ad421/custom-nike-air-max-90.png\"]", true, "Nike Air Max 270", 3500000m, 3200000m, "running", 4.5, 128, "[\"39\", \"40\", \"41\", \"42\", \"43\", \"44\"]" },
                    { "product-2", "Adidas", "men", "[\"Đen\", \"Trắng\", \"Xanh\"]", new DateTime(2024, 1, 2, 0, 0, 0, 0, DateTimeKind.Utc), "Giày chạy bộ Adidas Ultraboost 22 với công nghệ Boost", true, "[\"https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a78bc4896a3e9ad7800abcec6_9366/Ultraboost_22_Shoes_Black_GZ0127_01_standard.jpg\"]", true, "Adidas Ultraboost 22", null, 4200000m, "running", 4.7000000000000002, 95, "[\"39\", \"40\", \"41\", \"42\", \"43\", \"44\"]" },
                    { "product-3", "Converse", "women", "[\"Đen\", \"Trắng\", \"Đỏ\"]", new DateTime(2024, 1, 3, 0, 0, 0, 0, DateTimeKind.Utc), "Giày sneaker cổ điển Converse Chuck Taylor All Star", false, "[\"https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw2f8f0b9e/images/a_107/M7650_A_107X1.jpg\"]", true, "Converse Chuck Taylor All Star", null, 1800000m, "casual", 4.2999999999999998, 67, "[\"36\", \"37\", \"38\", \"39\", \"40\", \"41\", \"42\"]" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Products_Purpose",
                table: "Products",
                column: "Purpose");
        }
    }
}
