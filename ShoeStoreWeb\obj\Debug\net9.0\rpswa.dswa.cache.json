{"GlobalPropertiesHash": "9UjbizolTLQtRxPkhFtLog0cAeOlD0QD2dP26WWWJtQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["49NjkjTKXcChyAvyFdu1bf85AiQ+xIhfRbg4kNL+PrE=", "zTshZk9Ie96mpswQQeXqpdmoaa3i+AiL7Wg4anm1etk=", "YlNARMUxY8/0Tp5tdhs69+rhkVhPlOKFvC43Ibsk50E=", "tnT0NCF9GlisUJjr0NxSG7K6xlozwMV9OlSmnNcgSpA=", "quqsZReJd+TKTfF8l/TVKDXqz8pupIN+qFW1UlIHXvA=", "BYZZmTXTSXMbQZDj06yeB4hNsGSvCNMAIPaLJo+/IFs=", "p4Tqk6NS6wCA5zlPk6sW71mCn1Kw6wKsy8nyQIDGHxs=", "3RW60uhNi6lzb23wrWC3vF/nYXHjyqvqHYRv0YYZTfA=", "0Akw9aCTcC+hilsu3jjHzPRXAfNn00u+Ix/vTEaWqfg=", "Y42HFAp9B+085ydSsvfIQ/eSjI2/62uF2W6fQEZf77w=", "jEchyKiNI45EwczKQEAmkMbI5jrz0z7ydZkCAOESPqw=", "+PNy9lOtWejsgSBK7fH0uZkULhtjSQE4QmiRnvOx7Uc=", "NapLfSiqTJIkuJz1zueXutgEMXznM/rhQhLRM8sDWgg=", "5wU7/a9caa+adN7tAc8KmHPVWU+ZzA3jRp2H4OKW+3s=", "Ec/faU4D3T5SUoKKvA9PrfDxn7+k1TExZYFXFOCRcpg=", "fOpr7Qk3x1oJXqk4L7eFfUU25sqAEcEupTULhXknDhU=", "R8TTFfg5JhK0LDdD+DVUgPBCjiM6RQytYNg/IiTauV8=", "jYpAkJOowjdz5Qo1R/UVvoCbNFC4Eg0fz5H51FRjBDc=", "0obzlS4dhSWLN96Uohjoz+WkTnNO+B0lMv6AejKFfpg=", "/KNxLROQ9fL6mfFHtVXuasP76r0UE/oRBej5OMIWgWg=", "Yna51qw/TkE/5YADPrDo09jDWZtFWXUJAdI3bQkSHzg=", "VCJJkxdne1jctmtfPTQ9TSr324F4NO3f1yUX4okW5CU=", "Kcbwy7N31qKzKOiofvcLoudBaZqBgBHpEm8QjFTRO6Q=", "rr6PxOutdkK0JIGd/zKfD6fDqfZgc5VC0EQ93rO6mvc=", "d2lvNj+fG4ppxID7dYqc0+orqONw5DSbi0IstDQrr6Y=", "H+Ymxoa4beAWe7hG7Xno48/wJtRFRxVgfE+QIpig1dM=", "dVtZfyMgEB9lde8Jt5YWpXXx8InWmB49wxy8kzRZQ1g=", "Be2ZSJ+qDNwk5o8AVdf4jNLkKDGN0d6M10JrVDZRSWo=", "wSi/nC1DBeA7M3WWFfbc0k6oZ<PERSON>ley+ocEcgVcSyUkLQ=", "0vW79xhMEnnjmr10KCj81G/1hXdwRz8KJVbZhO4dhN8=", "dDEmrfvBh7oYat09L2u/UDwS67f8ii70py6XDI+j0dM=", "pjb1rTIKDkgohNTzS64hTBtHOP+ayO5WZkXVT6FgZyQ=", "eiSUAn4QzzydSGAacLMXsdWwxh7Xnl/i1x17nvySMkQ=", "bwDh83xx9QBV+ur5cKxfNMnk/Yer4PtVuFMhcXvoVZE=", "21W+4x+pag5Bo196mu/b8ojvw0z6SxX+aXoAlNm3+7g=", "50mRvMs62FxH3FvCXwgFMLE47SvbJAHY5U20FKIFNCY=", "BR6JdN4Kv6rTWVJNl9a1oN4Fz7Zfy/zWGLUkJpyC7ss=", "kqSyGcW72K9PiBls8sTFPPmc3n9DZs1IN9Dm3Qn9Jlg=", "oTUYuofXg45Frc6AuOyrF/6y7fV4IL0WMdKqadBcPkk=", "1CgLWaoYcMtyn9/aNQgiI65ttlax3RJHQHsO/3TZZfo=", "MYsABtfts5jeRpb24EKdmi4q5dpS+Noig/VlP+cHojc=", "4bUvEY6/1E6nvP2cJW7kL053REdiwOnH9nNv/33Q+Yw=", "M+j6IQFOjmggARVly3uF4RrbjzwbJlB6qLpk07kjSI8=", "G9ogZZTD/I69rAVZndRoJXy52aCoX0egvJ60IJNtugg=", "J4E56RgjJBieKxn6GWuggCLLVVn0YZ0Sb1Rg90gmivw=", "EQ2dQ414Q8jvmZhGfTzD14GV8NgFHavror4zE8UfpZU=", "M9MGoXV5aIYcaS/NZMQUMJU9nr+i3zAvnkikCoaEnqU=", "Wi4CPIxVb+37vlP9ZgnqnSs8oID5G5x/w+/psjaptjA=", "2hPsEkdzOnxYaJclVejzg6EtMaDe1nNN9tson+9Gy28=", "MDmnk1iHphAlGKsip73hlNU9L2+rRwlUxlKhsMWCuWs=", "yf8GjGz7TYKiVuLrrhf/aEVdMEDR9OdVr1KZ9c5zdC0=", "4canCMNaH4EtKweCHvetNGmipXQ5/1mkFgyZaQqQZXQ=", "qILiHMdqF96oTV7up0EV/oMukC3Mnfdgo+Hk2lVGdOU=", "UgVhbvXDVu1XQyuKjJkGGWUPzSSf/gasIcPXxifDfKE=", "o43GBYY9lwz8BSFU9dXJN2x98Wg7aMathJslnDRdqbo=", "YBMxrAa06CYjguZCEQCy0L+pjeeqnUi5limXCujbIek=", "+U49WLCwzsvVXFRnWB2hV1i/2gDAyOIsk02F3kfdJBE=", "p1fmF8fpsmOEZgB98yUJYUH00lcDmeWYhzcK7zJDWrI=", "qjztdiE2G7xYF3pWf9HeHEppRmGOBcLSdXoWB9+dbcE=", "Q05Bu49UaPvXNJoLyAQgFVy0QEfNH9sdhgeCYbmO1qM=", "9fRzuN9APLZlqKoXlFwdOLVfCeqjVKt1SAnjV7hJOF0=", "bXY7+XfwWYRaIq/0x2AYEQc1SpvM6nIaS8NizGVQeh8=", "R5VG3P3kuRc+c4oqgIYNBJmkPpa64cfAA5HFn0eczwY=", "QuUBBZFPYQZgQkCQwn/Nc2L2NZX1DS/WHZdFBP7vuXs=", "ADFRjDMPxX26KqNwgck/3M/v8geixuvDhwVOEmW6M8Q=", "A+UeIbUpMkkWYdMkXR+AvmK/y/q9xh5+KO9ZU3s7hBs=", "YQan0iXIottmYdlvadkwGrj52w5vPqbVxcitLznGhI0=", "owTtQ7fF8Ldr6PzoSJcR36G21a1vxoKqiV6yR0uneWk=", "xGFwdFovKFQrcMXSxNHremzafNZG1zIHqOOxTTBbOG4=", "kO5XjjtCoMh7bpDrH+djUxSylWM+yOioa/a84rz78X0=", "1CtKe+ori//oWpXJbsx8M5K0AzRG4js3LSC7AETCsIk=", "GzYHCQ1FPrdk248XfYiyOLQalTO7qZUm9CAeosB3pGQ=", "VV9XHIhtM7OumUpIHELbzkHnVmuFV3KT0aCpE+7RUZU=", "Cz5rhMD3jCT5ddej2KvGV4ARbyHq1bVOUdY3E7dhwGE=", "LiNxryKJDph7AdiKlODrGu/5KQERH1X1Z40S5l8Lnl8=", "aJtGVn56qO+5w2UEmLyDg2xY3N78dNsyUhxG48IFbBM=", "VmCHKocs2MaafQxCysoGSZXCqtbBzhLNicfCSNLzbDY=", "tbW9HJY18t7bznFsvCoF0fT9RCN3IhVBqiTfZf85DW0=", "i9FgiGp56T3o4I43eRNbE6ZTHsiV6uRh3XXz/j7NDj0=", "FFS4a7pLZenImxLy82Sjv6gEZjEy36qcOI3VcDE+ngU=", "nJqwx8NcNvag9k4RGhAOENVH9KGy6UvMInxyWFOCBxk=", "030gddmNC68t26Te5RdHatfvLZMjolGlIBwyvGJjwS4=", "RGVO+F172mkGNubGx8Pe7vY8+qN3QAX8WUPfz9BP8z8=", "2aUGVv25tqO0appT2U5cGaRCvBX4JpunstevUk9OpqA="], "CachedAssets": {"49NjkjTKXcChyAvyFdu1bf85AiQ+xIhfRbg4kNL+PrE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\css\\site.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-31T17:36:21.502964+00:00"}, "zTshZk9Ie96mpswQQeXqpdmoaa3i+AiL7Wg4anm1etk=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\favicon.ico", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-31T17:36:21.4443695+00:00"}, "YlNARMUxY8/0Tp5tdhs69+rhkVhPlOKFvC43Ibsk50E=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\js\\site.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-31T17:36:21.5039645+00:00"}, "tnT0NCF9GlisUJjr0NxSG7K6xlozwMV9OlSmnNcgSpA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-31T17:36:21.334433+00:00"}, "quqsZReJd+TKTfF8l/TVKDXqz8pupIN+qFW1UlIHXvA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-31T17:36:21.334433+00:00"}, "BYZZmTXTSXMbQZDj06yeB4hNsGSvCNMAIPaLJo+/IFs=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-31T17:36:21.3354328+00:00"}, "p4Tqk6NS6wCA5zlPk6sW71mCn1Kw6wKsy8nyQIDGHxs=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-31T17:36:21.3354328+00:00"}, "3RW60uhNi6lzb23wrWC3vF/nYXHjyqvqHYRv0YYZTfA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-31T17:36:21.336433+00:00"}, "0Akw9aCTcC+hilsu3jjHzPRXAfNn00u+Ix/vTEaWqfg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-31T17:36:21.3374321+00:00"}, "Y42HFAp9B+085ydSsvfIQ/eSjI2/62uF2W6fQEZf77w=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-31T17:36:21.337939+00:00"}, "jEchyKiNI45EwczKQEAmkMbI5jrz0z7ydZkCAOESPqw=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-31T17:36:21.3389471+00:00"}, "+PNy9lOtWejsgSBK7fH0uZkULhtjSQE4QmiRnvOx7Uc=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-31T17:36:21.3389471+00:00"}, "NapLfSiqTJIkuJz1zueXutgEMXznM/rhQhLRM8sDWgg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-31T17:36:21.3399465+00:00"}, "5wU7/a9caa+adN7tAc8KmHPVWU+ZzA3jRp2H4OKW+3s=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-31T17:36:21.3399465+00:00"}, "Ec/faU4D3T5SUoKKvA9PrfDxn7+k1TExZYFXFOCRcpg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-31T17:36:21.3399465+00:00"}, "fOpr7Qk3x1oJXqk4L7eFfUU25sqAEcEupTULhXknDhU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-31T17:36:21.3409461+00:00"}, "R8TTFfg5JhK0LDdD+DVUgPBCjiM6RQytYNg/IiTauV8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-31T17:36:21.3409461+00:00"}, "jYpAkJOowjdz5Qo1R/UVvoCbNFC4Eg0fz5H51FRjBDc=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-31T17:36:21.3419474+00:00"}, "0obzlS4dhSWLN96Uohjoz+WkTnNO+B0lMv6AejKFfpg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-31T17:36:21.3419474+00:00"}, "/KNxLROQ9fL6mfFHtVXuasP76r0UE/oRBej5OMIWgWg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-31T17:36:21.3429469+00:00"}, "Yna51qw/TkE/5YADPrDo09jDWZtFWXUJAdI3bQkSHzg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-31T17:36:21.3479491+00:00"}, "VCJJkxdne1jctmtfPTQ9TSr324F4NO3f1yUX4okW5CU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-31T17:36:21.3489482+00:00"}, "Kcbwy7N31qKzKOiofvcLoudBaZqBgBHpEm8QjFTRO6Q=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-31T17:36:21.3489482+00:00"}, "rr6PxOutdkK0JIGd/zKfD6fDqfZgc5VC0EQ93rO6mvc=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-31T17:36:21.3533672+00:00"}, "d2lvNj+fG4ppxID7dYqc0+orqONw5DSbi0IstDQrr6Y=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-31T17:36:21.3574573+00:00"}, "H+Ymxoa4beAWe7hG7Xno48/wJtRFRxVgfE+QIpig1dM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-31T17:36:21.3584547+00:00"}, "dVtZfyMgEB9lde8Jt5YWpXXx8InWmB49wxy8kzRZQ1g=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-31T17:36:21.3594553+00:00"}, "Be2ZSJ+qDNwk5o8AVdf4jNLkKDGN0d6M10JrVDZRSWo=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-31T17:36:21.3648665+00:00"}, "wSi/nC1DBeA7M3WWFfbc0k6oZZley+ocEcgVcSyUkLQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-31T17:36:21.366863+00:00"}, "0vW79xhMEnnjmr10KCj81G/1hXdwRz8KJVbZhO4dhN8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-31T17:36:21.3678645+00:00"}, "dDEmrfvBh7oYat09L2u/UDwS67f8ii70py6XDI+j0dM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-31T17:36:21.3698623+00:00"}, "pjb1rTIKDkgohNTzS64hTBtHOP+ayO5WZkXVT6FgZyQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-31T17:36:21.3708646+00:00"}, "eiSUAn4QzzydSGAacLMXsdWwxh7Xnl/i1x17nvySMkQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-31T17:36:21.3728656+00:00"}, "bwDh83xx9QBV+ur5cKxfNMnk/Yer4PtVuFMhcXvoVZE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-31T17:36:21.3738678+00:00"}, "21W+4x+pag5Bo196mu/b8ojvw0z6SxX+aXoAlNm3+7g=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-31T17:36:21.3748629+00:00"}, "50mRvMs62FxH3FvCXwgFMLE47SvbJAHY5U20FKIFNCY=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-31T17:36:21.3768633+00:00"}, "BR6JdN4Kv6rTWVJNl9a1oN4Fz7Zfy/zWGLUkJpyC7ss=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-31T17:36:21.3778639+00:00"}, "kqSyGcW72K9PiBls8sTFPPmc3n9DZs1IN9Dm3Qn9Jlg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-31T17:36:21.3778639+00:00"}, "oTUYuofXg45Frc6AuOyrF/6y7fV4IL0WMdKqadBcPkk=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-31T17:36:21.3788646+00:00"}, "1CgLWaoYcMtyn9/aNQgiI65ttlax3RJHQHsO/3TZZfo=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-31T17:36:21.3798638+00:00"}, "MYsABtfts5jeRpb24EKdmi4q5dpS+Noig/VlP+cHojc=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-31T17:36:21.3811009+00:00"}, "4bUvEY6/1E6nvP2cJW7kL053REdiwOnH9nNv/33Q+Yw=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-31T17:36:21.3821091+00:00"}, "M+j6IQFOjmggARVly3uF4RrbjzwbJlB6qLpk07kjSI8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-31T17:36:21.3831088+00:00"}, "G9ogZZTD/I69rAVZndRoJXy52aCoX0egvJ60IJNtugg=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-31T17:36:21.3841071+00:00"}, "J4E56RgjJBieKxn6GWuggCLLVVn0YZ0Sb1Rg90gmivw=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-31T17:36:21.3851078+00:00"}, "EQ2dQ414Q8jvmZhGfTzD14GV8NgFHavror4zE8UfpZU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-31T17:36:21.3861803+00:00"}, "M9MGoXV5aIYcaS/NZMQUMJU9nr+i3zAvnkikCoaEnqU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-31T17:36:21.3885701+00:00"}, "Wi4CPIxVb+37vlP9ZgnqnSs8oID5G5x/w+/psjaptjA=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-31T17:36:21.3354328+00:00"}, "2hPsEkdzOnxYaJclVejzg6EtMaDe1nNN9tson+9Gy28=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-31T17:36:21.4503768+00:00"}, "MDmnk1iHphAlGKsip73hlNU9L2+rRwlUxlKhsMWCuWs=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-31T17:36:21.4734419+00:00"}, "yf8GjGz7TYKiVuLrrhf/aEVdMEDR9OdVr1KZ9c5zdC0=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-31T17:36:21.337939+00:00"}, "4canCMNaH4EtKweCHvetNGmipXQ5/1mkFgyZaQqQZXQ=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-31T17:36:21.442369+00:00"}, "qILiHMdqF96oTV7up0EV/oMukC3Mnfdgo+Hk2lVGdOU=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-31T17:36:21.4433689+00:00"}, "UgVhbvXDVu1XQyuKjJkGGWUPzSSf/gasIcPXxifDfKE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-31T17:36:21.4453759+00:00"}, "o43GBYY9lwz8BSFU9dXJN2x98Wg7aMathJslnDRdqbo=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-31T17:36:21.446382+00:00"}, "YBMxrAa06CYjguZCEQCy0L+pjeeqnUi5limXCujbIek=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-31T17:36:21.337939+00:00"}, "+U49WLCwzsvVXFRnWB2hV1i/2gDAyOIsk02F3kfdJBE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-31T17:36:21.3895705+00:00"}, "p1fmF8fpsmOEZgB98yUJYUH00lcDmeWYhzcK7zJDWrI=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-31T17:36:21.3905701+00:00"}, "qjztdiE2G7xYF3pWf9HeHEppRmGOBcLSdXoWB9+dbcE=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-31T17:36:21.4038201+00:00"}, "Q05Bu49UaPvXNJoLyAQgFVy0QEfNH9sdhgeCYbmO1qM=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-31T17:36:21.4058195+00:00"}, "9fRzuN9APLZlqKoXlFwdOLVfCeqjVKt1SAnjV7hJOF0=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-31T17:36:21.4102543+00:00"}, "bXY7+XfwWYRaIq/0x2AYEQc1SpvM6nIaS8NizGVQeh8=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-31T17:36:21.4392753+00:00"}, "R5VG3P3kuRc+c4oqgIYNBJmkPpa64cfAA5HFn0eczwY=": {"Identity": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "ShoeStoreWeb", "SourceType": "Discovered", "ContentRoot": "D:\\test\\.NET SDK\\website b<PERSON>\\ShoeStoreWeb\\wwwroot\\", "BasePath": "_content/ShoeStoreWeb", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-31T17:36:21.336433+00:00"}}, "CachedCopyCandidates": {}}