@model IEnumerable<ShoeStoreWeb.Models.ApplicationUser>
@{
    ViewData["Title"] = "Quản lý khách hàng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-users me-2"></i>Quản lý khách hàng</h1>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Về dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Danh sách khách hàng (@Model.Count() người dùng)</h6>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Tên</th>
                                        <th>Email</th>
                                        <th>Số điện thoại</th>
                                        <th>Địa chỉ</th>
                                        <th>Vai trò</th>
                                        <th>Ngày đăng ký</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-circle me-2">
                                                        @user.Name?.Substring(0, 1).ToUpper()
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">@user.Name</div>
                                                        <small class="text-muted">@user.UserName</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>@user.Email</div>
                                                @if (user.EmailConfirmed)
                                                {
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>Đã xác thực
                                                    </small>
                                                }
                                                else
                                                {
                                                    <small class="text-warning">
                                                        <i class="fas fa-exclamation-circle me-1"></i>Chưa xác thực
                                                    </small>
                                                }
                                            </td>
                                            <td>@(user.Phone ?? "Chưa cập nhật")</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(user.Address))
                                                {
                                                    <span title="@user.Address">
                                                        @(user.Address.Length > 30 ? user.Address.Substring(0, 30) + "..." : user.Address)
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa cập nhật</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.Role == "admin")
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-crown me-1"></i>Quản trị viên
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user me-1"></i>Khách hàng
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                <span class="text-muted">Không rõ</span>
                                            </td>
                                            <td>
                                                @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                                                {
                                                    <span class="badge bg-danger">Bị khóa</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">Hoạt động</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info view-user-btn" 
                                                            data-user-id="@user.Id"
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#userDetailModal">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    @if (user.Role != "admin")
                                                    {
                                                        @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.UtcNow)
                                                        {
                                                            <button class="btn btn-sm btn-outline-success unlock-user-btn" 
                                                                    data-user-id="@user.Id">
                                                                <i class="fas fa-unlock"></i>
                                                            </button>
                                                        }
                                                        else
                                                        {
                                                            <button class="btn btn-sm btn-outline-warning lock-user-btn" 
                                                                    data-user-id="@user.Id">
                                                                <i class="fas fa-lock"></i>
                                                            </button>
                                                        }
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Chưa có khách hàng nào</h4>
                            <p class="text-muted">Danh sách khách hàng sẽ hiển thị tại đây khi có người đăng ký.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Detail Modal -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết khách hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}
</style>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Lock user
            $('.lock-user-btn').click(function() {
                var userId = $(this).data('user-id');
                
                if (confirm('Bạn có chắc chắn muốn khóa tài khoản này?')) {
                    $.post('@Url.Action("LockUser")', { userId: userId }, function(data) {
                        if (data.success) {
                            showToast('Khóa tài khoản thành công!', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || 'Có lỗi xảy ra', 'error');
                        }
                    });
                }
            });

            // Unlock user
            $('.unlock-user-btn').click(function() {
                var userId = $(this).data('user-id');
                
                if (confirm('Bạn có chắc chắn muốn mở khóa tài khoản này?')) {
                    $.post('@Url.Action("UnlockUser")', { userId: userId }, function(data) {
                        if (data.success) {
                            showToast('Mở khóa tài khoản thành công!', 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || 'Có lỗi xảy ra', 'error');
                        }
                    });
                }
            });

            // View user details (placeholder)
            $('.view-user-btn').click(function() {
                var userId = $(this).data('user-id');
                $('#userDetailContent').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> Đang tải...</div>');
                
                // In a real application, you would load user details via AJAX
                setTimeout(function() {
                    $('#userDetailContent').html(
                        '<div class="alert alert-info">' +
                        '<i class="fas fa-info-circle me-2"></i>' +
                        'Chi tiết khách hàng và lịch sử đơn hàng sẽ được hiển thị tại đây.' +
                        '</div>'
                    );
                }, 500);
            });
        });

        function showToast(message, type) {
            var bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
            var toast = $('<div class="toast position-fixed top-0 end-0 m-3" style="z-index: 9999;">' +
                '<div class="toast-header ' + bgClass + ' text-white">' +
                '<strong class="me-auto">Thông báo</strong>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '<div class="toast-body">' + message + '</div>' +
                '</div>');
            
            $('body').append(toast);
            var toastEl = new bootstrap.Toast(toast[0]);
            toastEl.show();
            
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}
